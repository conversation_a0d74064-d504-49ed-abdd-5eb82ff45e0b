from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query

from src.nurses.models import Nurse, Nurse<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NurseUpdate, NurseWithUser
from src.nurses.service import NurseService

from src.auth.auth import CurrentUser
from src.db.session import DBSession

import logging

nurse_router = APIRouter(prefix="/nurses", tags=["nurses"])

logger = logging.getLogger(__name__)


@nurse_router.post("/", response_model=NurseWithUser)
async def create_nurse(
    nurse_data: NurseCreate,
    session: DBSession,
    current_user: CurrentUser
):
    """Create a new nurse (Admin only)"""
    nurse = await NurseService(session).create_nurse_with_user(nurse_data)
    return nurse


@nurse_router.get("/", response_model=List[NurseWithUser])
async def get_nurses(
    session: DBSession,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    department: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """Get list of nurses with optional filtering"""
    nurses = await NurseService(session).get_nurses(
        skip=skip, 
        limit=limit,
        department=department,
        is_active=is_active
    )
    return nurses


@nurse_router.get("/available", response_model=List[NurseWithUser])
async def get_available_nurses(
    session: DBSession,
    department: Optional[str] = Query(None)
):
    """Get available nurses for assignment"""
    nurses = await NurseService(session).get_available_nurses(
        department=department
    )
    return nurses


@nurse_router.get("/search", response_model=List[NurseWithUser])
async def search_nurses(
    session: DBSession,
    q: str = Query(..., min_length=1)
):
    """Search nurses by name or employee ID"""
    nurses = await NurseService(session).search_nurses(q)
    return nurses


@nurse_router.get("/department/{department}", response_model=List[NurseWithUser])
async def get_nurses_by_department(
    department: str,
    session: DBSession
):
    """Get all active nurses in a specific department"""
    nurses = await NurseService(session).get_nurses_by_department(department)
    return nurses


@nurse_router.get("/me", response_model=NurseWithUser)
async def get_current_nurse_profile(
    session: DBSession,
    current_user: CurrentUser
):
    """Get current user's nurse profile"""
    logger.info(f"Getting nurse profile for user {current_user.id}")
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")
    return nurse


@nurse_router.post("/me", response_model=NurseWithUser)
async def create_current_nurse_profile(
    nurse_data: NurseCreate,
    session: DBSession,
    current_user: CurrentUser
):
    """Create nurse profile for current user"""
    # Override the openid and session_key with current user's data if available
    if hasattr(current_user, 'openid') and current_user.openid:
        nurse_data.openid = current_user.openid
    if hasattr(current_user, 'session_key') and current_user.session_key:
        nurse_data.session_key = current_user.session_key

    try:
        nurse = await NurseService(session).create_nurse_profile_for_user(current_user.id, nurse_data)
        return nurse
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating nurse profile: {e}")
        raise HTTPException(status_code=500, detail="Failed to create nurse profile")


@nurse_router.patch("/me", response_model=NurseWithUser)
async def update_current_nurse_profile(
    nurse_update: NurseUpdate,
    session: DBSession,
    current_user: CurrentUser
):
    """Update current user's nurse profile"""
    nurse = await NurseService(session).get_nurse_by_user_id(current_user.id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse profile not found")

    updated_nurse = await NurseService(session).update_nurse(nurse.id, nurse_update)
    return updated_nurse


@nurse_router.get("/{nurse_id}", response_model=NurseWithUser)
async def get_nurse(
    nurse_id: int,
    session: DBSession
):
    """Get a specific nurse by ID"""
    nurse = await NurseService(session).get_nurse_by_nurseid(nurse_id)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.patch("/{nurse_id}", response_model=NurseWithUser)
async def update_nurse(
    nurse_id: int,
    nurse_update: NurseUpdate,
    session: DBSession
):
    """Update a nurse (Admin only)"""
    nurse = await NurseService(session).update_nurse(nurse_id, nurse_update)
    if not nurse:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return nurse


@nurse_router.delete("/{nurse_id}")
async def delete_nurse(
    nurse_id: int,
    session: DBSession
):
    """Soft delete a nurse (Admin only)"""
    success = await NurseService(session).delete_nurse(nurse_id)
    if not success:
        raise HTTPException(status_code=404, detail="Nurse not found")
    return {"message": "Nurse deleted successfully"}
