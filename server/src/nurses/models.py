from typing import Optional
from sqlmodel import SQLModel, Field, Relationship
import enum

from src.users.models import User, UserCreate, UserRead


class NurseGender(enum.StrEnum):
    MALE = "男"
    FEMALE = "女"


class NurseCore(SQLModel):
    name: Optional[str] = Field(default=None, index=True, max_length=50)
    gender: Optional[NurseGender] = Field(default=None)
    title: Optional[str] = Field(default=None, max_length=100)  # 职称
    department: Optional[str] = Field(default=None, max_length=100)  # 科室
    license_number: Optional[str] = Field(default=None, max_length=50)  # 护士执业证号
    years_of_experience: Optional[int] = Field(default=0)  # 工作年限
    specialties: Optional[str] = Field(default=None, max_length=200)  # 专业特长
    is_active: Optional[bool] = Field(default=False)


class NurseBase(NurseCore):
    name: str = Field(index=True, max_length=50)
    gender: NurseGender
    department: str = Field(max_length=100)
    license_number: str = Field(max_length=50)


class NurseCreate(UserCreate, NurseBase):
    pass


class NurseRead(NurseBase):
    id: int
    user_id: int


class NurseUpdate(NurseCore):
    # All fields are optional for PATCH operations
    pass


class NurseWithUser(NurseRead):
    user: UserRead


class Nurse(NurseBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    # 1:1 relationship with User
    user_id: int = Field(foreign_key="user.id", unique=True)
    # Relationship back to User
    user: User = Relationship(back_populates="nurse")
