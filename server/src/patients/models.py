from typing import Optional
from sqlmodel import SQLModel, Field, Relationship
from src.users.models import User

class PatientBase(SQLModel):
    name: str = Field(index=True)
    phone: str = Field(max_length=20)
    id_num: str = Field(max_length=50)
    address: str

class Patient(PatientBase, table=True):
    id: Optional[int] = Field(default=None, primary_key=True)
    # Foreign key to link a patient to a user
    user_id: int = Field(foreign_key="user.id")
    # Define the relationship
    user: User = Relationship(back_populates="patients")

class PatientCreate(PatientBase):
    pass

class PatientRead(PatientBase):
    id: int
    user_id: int

class PatientUpdate(SQLModel):
    name: Optional[str] = None
    phone: Optional[str] = None
    id_num: Optional[str] = None
    address: Optional[str] = None