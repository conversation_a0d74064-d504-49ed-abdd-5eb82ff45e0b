from fastapi import <PERSON><PERSON><PERSON>, APIRouter
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# Import routers from your apps
from src.patients.routes import patient_router
from src.users.routes import user_router
from src.services.routes import service_router, service_tag_router
from src.auth.routes import auth_router
from src.orders.routes import order_router
from src.orders.test_routes import test_router
from src.nurses.routes import nurse_router

import logging

logging.basicConfig(
    level=logging.DEBUG,
)

app = FastAPI(title="Home Care API")

# --- CORS Middleware ---
# Allows all origins for simplicity. Restrict this in production.
origins = ["*"] 

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Static File Serving ---
app.mount("/static", StaticFiles(directory="static"), name="static")

# --- API Router ---
api_router = APIRouter(prefix="/api/v1")

api_router.include_router(patient_router)
api_router.include_router(user_router)
api_router.include_router(service_router)
api_router.include_router(service_tag_router)
api_router.include_router(auth_router)
api_router.include_router(order_router)
api_router.include_router(test_router)
api_router.include_router(nurse_router)

app.include_router(api_router)

@app.get("/")
async def read_root():
    return {"message": "Welcome to the Home Care API"}