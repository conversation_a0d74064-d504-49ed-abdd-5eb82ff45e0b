from fastapi import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Request
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

# Import routers from your apps
from src.patients.routes import patient_router
from src.users.routes import user_router
from src.services.routes import service_router, service_tag_router
from src.auth.routes import auth_router
from src.orders.routes import order_router
from src.orders.test_routes import test_router
from src.nurses.routes import nurse_router

import logging

logging.basicConfig(
    level=logging.DEBUG,
)

app = FastAPI(title="Home Care API")

# --- Exception Handlers ---
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors with detailed messages"""

    # Log the request details
    logger.error(f"=== VALIDATION ERROR ===")
    logger.error(f"Request: {request.method} {request.url}")
    logger.error(f"Headers: {dict(request.headers)}")

    # Try to get request body for debugging
    try:
        body = await request.body()
        if body:
            logger.error(f"Request body: {body.decode('utf-8')}")
    except Exception as e:
        logger.error(f"Could not read request body: {e}")

    logger.error(f"Validation errors: {exc.errors()}")

    # Extract detailed error information
    errors = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_detail = f"{field}: {message}"
        errors.append(error_detail)
        logger.error(f"  - {error_detail}")

    logger.error(f"=== END VALIDATION ERROR ===")

    return JSONResponse(
        status_code=422,
        content={
            "detail": "Validation failed",
            "errors": errors,
            "message": "; ".join(errors)
        }
    )





# --- CORS Middleware ---
# Allows all origins for simplicity. Restrict this in production.
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Static File Serving ---
app.mount("/static", StaticFiles(directory="static"), name="static")

# --- API Router ---
api_router = APIRouter(prefix="/api/v1")

api_router.include_router(patient_router)
api_router.include_router(user_router)
api_router.include_router(service_router)
api_router.include_router(service_tag_router)
api_router.include_router(auth_router)
api_router.include_router(order_router)
api_router.include_router(test_router)
api_router.include_router(nurse_router)

app.include_router(api_router)

@app.get("/")
async def read_root():
    return {"message": "Welcome to the Home Care API"}