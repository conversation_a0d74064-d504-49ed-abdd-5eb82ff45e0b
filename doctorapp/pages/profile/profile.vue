<template>
  <view class="profile-container">
    <view class="profile-header">
      <text class="title">完善护士信息</text>
      <text class="subtitle">请填写您的执业信息</text>
    </view>

    <view class="form-container">
      <view class="form-item">
        <text class="label">姓名 *</text>
        <input class="input" v-model="nurseForm.name" placeholder="请输入真实姓名" />
      </view>

      <view class="form-item">
        <text class="label">性别 *</text>
        <picker @change="onGenderChange" :value="genderIndex" :range="genderOptions">
          <view class="picker">{{ genderOptions[genderIndex] }}</view>
        </picker>
      </view>

      <view class="form-item">
        <text class="label">职称 *</text>
        <input class="input" v-model="nurseForm.title" placeholder="如：主管护师" />
      </view>

      <view class="form-item">
        <text class="label">科室 *</text>
        <input class="input" v-model="nurseForm.department" placeholder="请输入科室" />
      </view>

      <view class="form-item">
        <text class="label">执业证书号 *</text>
        <input class="input" v-model="nurseForm.license_number" placeholder="请输入执业证书号" />
      </view>

      <view class="form-item">
        <text class="label">工作年限</text>
        <input class="input" type="number" v-model="nurseForm.years_of_experience" placeholder="请输入工作年限" />
      </view>

      <view class="form-item">
        <text class="label">专业特长</text>
        <textarea class="textarea" v-model="nurseForm.specialties" placeholder="请输入专业特长（可选）"></textarea>
      </view>

      <button class="submit-btn" @click="submitProfile" :loading="isSubmitting">
        {{ isSubmitting ? '提交中...' : '完成注册' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { API_BASE_URL } from '@/utils/config.js';

const isSubmitting = ref(false)
const genderIndex = ref(0)
const genderOptions = ['男', '女']

const nurseForm = ref({
  name: '',
  gender: 'male',
  title: '',
  department: '',
  license_number: '',
  years_of_experience: 0,
  specialties: ''
})

const onGenderChange = (e) => {
  genderIndex.value = e.detail.value
  nurseForm.value.gender = genderIndex.value === 0 ? 'male' : 'female'
}

const submitProfile = async () => {
  // Validation
  if (!nurseForm.value.name || !nurseForm.value.title || 
      !nurseForm.value.department || !nurseForm.value.license_number) {
    uni.showToast({
      title: '请填写必填项',
      icon: 'none'
    })
    return
  }

  try {
    isSubmitting.value = true
    const token = uni.getStorageSync('token')
    
    // Get user info for additional fields
    const userInfo = uni.getStorageSync('userInfo') || {}
    
    // Prepare data matching NurseCreate model
    const submitData = {
      name: nurseForm.value.name,
      gender: nurseForm.value.gender,
      title: nurseForm.value.title,
      department: nurseForm.value.department,
      license_number: nurseForm.value.license_number,
      years_of_experience: parseInt(nurseForm.value.years_of_experience) || 0,
      specialties: nurseForm.value.specialties || '',
      is_active: true,
      // Add user info fields
      nickname: userInfo.nickname || '',
      avatar: userInfo.avatarUrl || '',
      phone: '', // Add phone field if needed
      openid: '', // Will be set by backend from current_user
      session_key: '' // Will be set by backend from current_user
    }

    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/nurses/me`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: submitData,
        success: resolve,
        fail: reject
      })
    })

    if (response.statusCode === 200) {
      uni.showToast({
        title: '注册成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }, 1000)
    } else {
      console.error('Server response:', response)
      throw new Error(response.data?.detail || '注册失败')
    }

  } catch (error) {
    console.error('Profile submission failed:', error)
    uni.showToast({
      title: error.message || '注册失败',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 40rpx;
}

.profile-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.form-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
  
  .label {
    display: block;
    font-size: 32rpx;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .input, .picker, .textarea {
    width: 100%;
    padding: 24rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    font-size: 30rpx;
  }
  
  .textarea {
    height: 120rpx;
  }
  
  .picker {
    background: white;
    color: #333;
  }
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 40rpx;
}
</style>
