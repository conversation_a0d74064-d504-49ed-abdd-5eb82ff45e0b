#!/usr/bin/env python3

import requests
import json
import sys

def test_nurse_registration():
    """Test the nurse registration endpoint"""
    
    # First, let's test with the data that should work
    test_data = {
        "name": "测试护士",
        "gender": "男",  # Using Chinese characters as expected by the enum
        "title": "主管护师",
        "department": "内科",
        "license_number": "12345678",
        "years_of_experience": 5,
        "specialties": "心血管护理",
        "is_active": True,
        "nickname": "测试用户",
        "avatar": "",
        "phone": ""
    }
    
    # Test without authentication first to see the structure
    print("Testing nurse registration endpoint...")
    print(f"Test data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/nurses/me",
            json=test_data,
            headers={"Content-Type": "application/json"},
            verify=False  # Skip SSL verification for localhost
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 422:
            print("\n=== VALIDATION ERROR DETAILS ===")
            try:
                error_data = response.json()
                print(f"Error data: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
                if "errors" in error_data:
                    print("\nValidation errors:")
                    for error in error_data["errors"]:
                        print(f"  - {error}")
            except:
                print("Could not parse error response as JSON")
                
        elif response.status_code == 401:
            print("\n=== AUTHENTICATION REQUIRED ===")
            print("This is expected - we need a valid token to create a nurse profile")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False
        
    return True

def test_with_invalid_gender():
    """Test with invalid gender to see validation"""
    test_data = {
        "name": "测试护士",
        "gender": "male",  # This should fail - wrong enum value
        "title": "主管护师", 
        "department": "内科",
        "license_number": "12345678",
        "years_of_experience": 5,
        "specialties": "心血管护理",
        "is_active": True,
        "nickname": "测试用户",
        "avatar": "",
        "phone": ""
    }
    
    print("\n" + "="*50)
    print("Testing with invalid gender (should fail)...")
    print(f"Test data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/nurses/me",
            json=test_data,
            headers={"Content-Type": "application/json"},
            verify=False
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 422:
            print("\n=== VALIDATION ERROR (EXPECTED) ===")
            try:
                error_data = response.json()
                print(f"Error data: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("Testing Nurse Registration API")
    print("="*50)
    
    # Test with valid data
    test_nurse_registration()
    
    # Test with invalid data
    test_with_invalid_gender()
    
    print("\n" + "="*50)
    print("Test completed!")
