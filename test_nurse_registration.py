#!/usr/bin/env python3

import requests
import json

# Test data similar to what the frontend sends
test_data = {
    "name": "测试护士",
    "gender": "男",
    "title": "主管护师",
    "department": "内科",
    "license_number": "12345678",
    "years_of_experience": 5,
    "specialties": "心血管护理",
    "is_active": True,
    "nickname": "测试用户",
    "avatar": "",
    "phone": ""
}

# You'll need to replace this with a valid token
# For testing, you can get a token by logging in through the app first
token = "your_token_here"

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

try:
    response = requests.post(
        "https://localhost:8000/api/v1/nurses/me",
        json=test_data,
        headers=headers,
        verify=False  # Skip SSL verification for localhost
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 422:
        print("\nValidation Error Details:")
        error_data = response.json()
        if "errors" in error_data:
            for error in error_data["errors"]:
                print(f"  - {error}")
        
except Exception as e:
    print(f"Error: {e}")
