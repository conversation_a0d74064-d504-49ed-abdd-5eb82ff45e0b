#!/usr/bin/env python3

import requests
import json

def get_real_token():
    """Get a real token by logging in"""
    login_data = {
        "code": "test_code",
        "app_key": "doctor"
    }
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/auth/login",
            json=login_data,
            verify=False
        )
        
        print(f"Login response: {response.status_code}")
        print(f"Login data: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            return data.get("token")
        else:
            print("Login failed, using mock token")
            return None
            
    except Exception as e:
        print(f"Login request failed: {e}")
        return None

def test_nurse_registration_with_token(token):
    """Test nurse registration with a real token"""
    
    test_data = {
        "name": "测试护士",
        "gender": "男",
        "title": "主管护师", 
        "department": "内科",
        "license_number": "12345678",
        "years_of_experience": 5,
        "specialties": "心血管护理",
        "is_active": True,
        "nickname": "测试用户",
        "avatar": "",
        "phone": ""
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\nTesting nurse registration with token...")
    print(f"Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/nurses/me",
            json=test_data,
            headers=headers,
            verify=False
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 422:
            print("\n=== VALIDATION ERROR DETAILS ===")
            try:
                error_data = response.json()
                print(f"Full error response: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("Testing Nurse Registration with Real Token")
    print("="*50)
    
    # Try to get a real token first
    token = get_real_token()
    
    if not token:
        # Use a mock token format that might bypass some validation
        token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOiI3In0.mock_signature"
        print(f"Using mock token: {token}")
    
    test_nurse_registration_with_token(token)
    
    print("\n" + "="*50)
    print("Test completed!")
