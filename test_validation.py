#!/usr/bin/env python3

import requests
import json

def test_validation_with_auth():
    """Test with a mock token to see validation errors"""
    
    # Test data that should work
    test_data = {
        "name": "测试护士",
        "gender": "男",
        "title": "主管护师",
        "department": "内科",
        "license_number": "12345678",
        "years_of_experience": 5,
        "specialties": "心血管护理",
        "is_active": True,
        "nickname": "测试用户",
        "avatar": "",
        "phone": ""
    }
    
    # Use a fake token to bypass auth and see validation errors
    headers = {
        "Authorization": "Bearer fake_token_for_testing",
        "Content-Type": "application/json"
    }
    
    print("Testing nurse registration with fake token...")
    print(f"Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/nurses/me",
            json=test_data,
            headers=headers,
            verify=False
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 422:
            print("\n=== VALIDATION ERROR DETAILS ===")
            try:
                error_data = response.json()
                print(f"Full error response: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except Exception as e:
        print(f"Request failed: {e}")

def test_invalid_gender():
    """Test with invalid gender"""
    test_data = {
        "name": "测试护士",
        "gender": "invalid_gender",  # This should fail
        "title": "主管护师",
        "department": "内科", 
        "license_number": "12345678",
        "years_of_experience": 5,
        "specialties": "心血管护理",
        "is_active": True,
        "nickname": "测试用户",
        "avatar": "",
        "phone": ""
    }
    
    headers = {
        "Authorization": "Bearer fake_token_for_testing",
        "Content-Type": "application/json"
    }
    
    print("\n" + "="*50)
    print("Testing with invalid gender...")
    print(f"Data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(
            "https://localhost:8000/api/v1/nurses/me",
            json=test_data,
            headers=headers,
            verify=False
        )
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 422:
            print("\n=== VALIDATION ERROR (EXPECTED) ===")
            try:
                error_data = response.json()
                print(f"Full error response: {json.dumps(error_data, ensure_ascii=False, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    print("Testing Nurse Validation")
    print("="*50)
    
    test_validation_with_auth()
    test_invalid_gender()
    
    print("\n" + "="*50)
    print("Test completed!")
