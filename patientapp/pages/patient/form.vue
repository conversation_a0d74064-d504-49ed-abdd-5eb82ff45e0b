<template>
  <view class="form-container">
    <view class="form-item">
      <text class="label">姓名:</text>
      <input v-model="formData.name" placeholder="请输入患者姓名" class="input" type="text" />
      <text v-if="errors.name" class="error">{{ errors.name }}</text>
    </view>
    <view class="form-item">
      <text class="label">身份证号:</text>
      <input v-model="formData.id_num" placeholder="请输入身份证号" class="input" />
      <text v-if="errors.id_num" class="error">{{ errors.id_num }}</text>
    </view>
    <view class="form-item">
      <text class="label">手机号:</text>
      <input v-model="formData.phone" placeholder="请输入手机号" class="input" />
      <text v-if="errors.phone" class="error">{{ errors.phone }}</text>
    </view>
    <view class="form-item">
      <text class="label">地址:</text>
      <textarea v-model="formData.address" placeholder="请输入详细地址" class="textarea" />
      <text v-if="errors.address" class="error">{{ errors.address }}</text>
    </view>
    <button type="primary" @click="submit">提交</button>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive
  } from 'vue';
  import {
    onLoad
  } from '@dcloudio/uni-app';
  import {
    API_BASE_URL
  } from '@/utils/config.js';

  const patientId = ref(null);
  const formData = reactive({
    name: '',
    id_num: '',
    phone: '',
    address: ''
  });
  const errors = reactive({});

  onLoad(async (options) => {
    if (options.id) {
      patientId.value = options.id;
      uni.setNavigationBarTitle({
        title: '编辑患者信息'
      });
      await fetchPatientDetails(options.id);
    } else {
      uni.setNavigationBarTitle({
        title: '添加新患者'
      });
    }
  });

  const fetchPatientDetails = async (id) => {
    const token = uni.getStorageSync('token');
    try {
      const response = await uni.request({
        url: `${API_BASE_URL}/patients/${id}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (response.statusCode === 200) {
        Object.assign(formData, response.data);
      } else {
        uni.showToast({
          title: '获取患者信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  };

  const validateField = (field, value) => {
    errors[field] = '';
    if (!value) {
      errors[field] = '此项不能为空';
      return false;
    }
    if (field === 'id_num' && !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
      errors[field] = '身份证号格式不正确';
      return false;
    }
    if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
      errors[field] = '手机号格式不正确';
      return false;
    }
    return true;
  };

  const submit = async () => {
    Object.keys(formData).forEach(key => {
      validateField(key, formData[key]);
    });

    if (Object.values(errors).some(error => error !== '')) return;

    const token = uni.getStorageSync('token');
    const isUpdate = !!patientId.value;
    const url = isUpdate ? `${API_BASE_URL}/patients/${patientId.value}` : `${API_BASE_URL}/patients/`;
    const method = isUpdate ? 'PUT' : 'POST';

    try {
      const response = await uni.request({
        url,
        method,
        data: formData,
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.statusCode === 200) {
        uni.showToast({
          title: isUpdate ? '更新成功' : '添加成功',
          icon: 'success'
        });
        uni.navigateBack();
      } else {
        uni.showToast({
          title: response.data.detail || (isUpdate ? '更新失败' : '添加失败'),
          icon: 'none',
          duration: 3000
        });
      }
    } catch (error) {
      uni.showToast({
        title: '网络错误',
        icon: 'none'
      });
    }
  };
</script>

<style scoped>
  .form-container {
    padding: 15px;
    background-color: #fff;
  }

  .form-item {
    margin-bottom: 15px;
  }

  .label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .input, .textarea {
    width: 100%;
    padding: 0 20rpx;
    background-color: #f7f7f7;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
  
  .input {
    height: 60rpx;
  }
  
  .textarea {
    height: 300rpx;
    padding: 20rpx;
    align-items: flex-start;
  }

  .error {
    color: red;
    font-size: 12px;
    margin-top: 5px;
  }
</style>