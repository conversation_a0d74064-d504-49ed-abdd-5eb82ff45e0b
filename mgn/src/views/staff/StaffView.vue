<template>
  <div class="staff-management">
    <!-- Toolbar -->
    <div class="toolbar">
      <div class="left-controls">
        <input
          type="text"
          v-model="searchQuery"
          placeholder="姓名, 科室, 职称"
          @keydown.enter="searchStaff"
          class="search-input"
        >
        <select v-model="departmentFilter" class="filter-select">
          <option value="">所有科室</option>
          <option v-for="dept in departments" :key="dept" :value="dept">
            {{ dept }}
          </option>
        </select>
        <select v-model="statusFilter" class="filter-select">
          <option value="">状态</option>
          <option value="active">{{ STAFF_STATUS.ACTIVE }}</option>
          <option value="inactive">{{ STAFF_STATUS.INACTIVE }}</option>
        </select>
        <select v-model="sortOrder" class="filter-select">
          <option value="created_at_desc">加入时间降序</option>
          <option value="created_at_asc">加入时间升序</option>
          <option value="name_asc">姓名升序</option>
          <option value="name_desc">姓名降序</option>
        </select>
        <button @click="searchStaff" class="btn btn-primary">搜索</button>
      </div>
    </div>

    <!-- Table -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
            </th>
            <th class="id-col">ID</th>
            <th class="name-col">姓名</th>
            <th class="gender-col">性别</th>
            <th class="title-col">职称</th>
            <th class="department-col">科室</th>
            <th class="experience-col">工作年限</th>
            <th class="status-col">状态</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="isLoading">
            <td :colspan="9" style="text-align: center; padding: 40px;">
              正在加载...
            </td>
          </tr>
          <tr v-else-if="staff.length === 0">
            <td :colspan="9" style="text-align: center; padding: 40px;">
              没有找到医护人员
            </td>
          </tr>
          <tr v-else v-for="nurse in staff" :key="nurse.id" class="table-row">
            <td class="checkbox-col">
              <input type="checkbox" v-model="selectedItems" :value="nurse.id">
            </td>
            <td class="id-col">{{ nurse.id }}</td>
            <td class="name-col">{{ nurse.name }}</td>
            <td class="gender-col">{{ nurse.gender }}</td>
            <td class="title-col">{{ nurse.title }}</td>
            <td class="department-col">{{ nurse.department }}</td>
            <td class="experience-col">{{ nurse.years_of_experience }}年</td>
            <td class="status-col">
              <span
                class="status-badge"
                :class="getStaffStatusClass(nurse.is_active)"
              >
                {{ getStaffStatusText(nurse.is_active) }}
              </span>
            </td>
            <td class="actions-col">
              <div class="action-buttons">
                <button @click="viewStaff(nurse)" class="btn-action btn-view" title="查看">
                  详情
                </button>
                <button @click="editStaff(nurse)" class="btn-action btn-edit" title="编辑">
                  编辑
                </button>
                <button
                  @click="toggleStaffStatus(nurse)"
                  class="btn-action"
                  :class="nurse.is_active ? 'btn-disable' : 'btn-enable'"
                  :title="getStaffToggleActionText(nurse.is_active)"
                >
                  {{ getStaffToggleActionText(nurse.is_active) }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="pagination">
      <button @click="prevPage" :disabled="currentPage === 1" class="btn btn-secondary">上一页</button>
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页，共 {{ totalRecords }} 条记录
      </span>
      <button @click="nextPage" :disabled="currentPage === totalPages" class="btn btn-secondary">下一页</button>
    </div>

    <!-- View Staff Modal -->
    <teleport to="body">
      <div class="modal" v-if="showViewStaffModal && currentStaff">
        <div class="modal-content">
          <span class="close" @click="closeViewStaffModal">&times;</span>
          <h2>医护人员详情</h2>
          <div class="view-details">
            <p><strong>ID:</strong> <span>{{ currentStaff.id }}</span></p>
            <p><strong>姓名:</strong> <span>{{ currentStaff.name }}</span></p>
            <p><strong>性别:</strong> <span>{{ currentStaff.gender }}</span></p>
            <p><strong>职称:</strong> <span>{{ currentStaff.title }}</span></p>
            <p><strong>科室:</strong> <span>{{ currentStaff.department }}</span></p>
            <p><strong>执业证号:</strong> <span>{{ currentStaff.license_number }}</span></p>
            <p><strong>工作年限:</strong> <span>{{ currentStaff.years_of_experience }}年</span></p>
            <p><strong>专业特长:</strong> <span>{{ currentStaff.specialties }}</span></p>
            <p><strong>状态:</strong> <span>{{ getStaffStatusText(currentStaff.is_active) }}</span></p>
          </div>
        </div>
      </div>
    </teleport>

    <!-- Edit Staff Modal -->
    <teleport to="body">
      <div class="modal" v-if="showEditStaffModal && currentStaff">
        <div class="modal-content">
          <span class="close" @click="closeEditStaffModal">&times;</span>
          <h2>编辑医护人员</h2>

          <label for="editStaffName">姓名:</label>
          <input type="text" id="editStaffName" v-model="currentStaff.name">

          <label for="editStaffGender">性别:</label>
          <select id="editStaffGender" v-model="currentStaff.gender">
            <option value="男">男</option>
            <option value="女">女</option>
          </select>

          <label for="editStaffTitle">职称:</label>
          <input type="text" id="editStaffTitle" v-model="currentStaff.title">

          <label for="editStaffDepartment">科室:</label>
          <input type="text" id="editStaffDepartment" v-model="currentStaff.department">

          <label for="editStaffLicense">执业证号:</label>
          <input type="text" id="editStaffLicense" v-model="currentStaff.license_number">

          <label for="editStaffExperience">工作年限:</label>
          <input type="number" id="editStaffExperience" v-model="currentStaff.years_of_experience">

          <label for="editStaffSpecialties">专业特长:</label>
          <textarea id="editStaffSpecialties" v-model="currentStaff.specialties"></textarea>

          <button @click="saveEditedStaff" class="btn btn-primary">保存</button>
        </div>
      </div>
    </teleport>

    <!-- Add Staff Modal -->
    <teleport to="body">
      <div class="modal" v-if="showAddStaffModal">
        <div class="modal-content">
          <span class="close" @click="closeAddStaffModal">&times;</span>
          <h2>添加医护人员</h2>

          <label for="staffName">姓名:</label>
          <input type="text" id="staffName" v-model="newStaff.name">

          <label for="staffGender">性别:</label>
          <select id="staffGender" v-model="newStaff.gender">
            <option value="男">男</option>
            <option value="女">女</option>
          </select>

          <label for="staffTitle">职称:</label>
          <input type="text" id="staffTitle" v-model="newStaff.title">

          <label for="staffDepartment">科室:</label>
          <input type="text" id="staffDepartment" v-model="newStaff.department">

          <label for="staffLicense">执业证号:</label>
          <input type="text" id="staffLicense" v-model="newStaff.license_number">

          <label for="staffExperience">工作年限:</label>
          <input type="number" id="staffExperience" v-model="newStaff.years_of_experience">

          <label for="staffSpecialties">专业特长:</label>
          <textarea id="staffSpecialties" v-model="newStaff.specialties"></textarea>

          <button @click="saveNewStaff" class="btn btn-primary">保存</button>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { watchDebounced } from '@vueuse/core';

import {
  STAFF_STATUS,
  getStaffStatusText,
  getStaffStatusClass,
  getStaffToggleActionText
} from '@/utils/constants';

// Types
interface Staff {
  id: number
  name: string
  gender: string
  title: string
  department: string
  license_number: string
  years_of_experience: number
  specialties: string
  is_active: boolean
}

type SortOrder = 'created_at_desc' | 'created_at_asc' | 'name_asc' | 'name_desc'
type StatusFilter = '' | 'active' | 'inactive'

// Reactive state
const searchQuery = ref<string>('')
const departmentFilter = ref<string>('')
const departments = ref<string[]>([])
const statusFilter = ref<StatusFilter>('')
const sortOrder = ref<SortOrder>('created_at_desc')
const selectAll = ref<boolean>(false)
const selectedItems = ref<number[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(20)
const showAddStaffModal = ref<boolean>(false)
const totalRecords = ref<number>(0)
const staff = ref<Staff[]>([])
const isLoading = ref<boolean>(true)

const showViewStaffModal = ref<boolean>(false)
const showEditStaffModal = ref<boolean>(false)
const currentStaff = ref<Staff | null>(null)

const newStaff = ref({
  name: '',
  gender: '男',
  title: '',
  department: '',
  license_number: '',
  years_of_experience: 0,
  specialties: ''
});

// Computed properties
const totalPages = computed<number>(() => {
  if (totalRecords.value === 0) return 1
  return Math.ceil(totalRecords.value / pageSize.value)
})

// Methods
const fetchStaff = async () => {
  isLoading.value = true
  try {
    const params = new URLSearchParams({
      skip: ((currentPage.value - 1) * pageSize.value).toString(),
      limit: pageSize.value.toString(),
      sort_order: sortOrder.value,
    })

    if (searchQuery.value) params.append('search', searchQuery.value)
    if (departmentFilter.value) params.append('department', departmentFilter.value)
    if (statusFilter.value) params.append('status', statusFilter.value)

    const response = await fetch(`/api/v1/nurses/?${params.toString()}`)
    if (!response.ok) throw new Error('Network response was not ok')

    const data = await response.json()
    staff.value = data.nurses || data
    totalRecords.value = data.total || staff.value.length

    // Extract unique departments
    const uniqueDepts = [...new Set(staff.value.map(s => s.department).filter(Boolean))]
    departments.value = uniqueDepts
  } catch (error) {
    console.error('Failed to fetch staff:', error)
  } finally {
    isLoading.value = false
  }
}

const toggleSelectAll = (): void => {
  if (selectAll.value) {
    selectedItems.value = staff.value.map(s => s.id)
  } else {
    selectedItems.value = []
  }
}

const searchStaff = (): void => {
  currentPage.value = 1
  fetchStaff()
}

const viewStaff = (nurse: Staff): void => {
  currentStaff.value = nurse
  showViewStaffModal.value = true
}

const closeViewStaffModal = () => {
  showViewStaffModal.value = false
  currentStaff.value = null
}

const editStaff = (nurse: Staff): void => {
  currentStaff.value = { ...nurse }
  showEditStaffModal.value = true
}

const closeEditStaffModal = () => {
  showEditStaffModal.value = false
  currentStaff.value = null
}

const saveEditedStaff = async () => {
  if (!currentStaff.value) return

  try {
    const response = await fetch(`/api/v1/nurses/${currentStaff.value.id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(currentStaff.value)
    })

    if (response.ok) {
      closeEditStaffModal()
      fetchStaff()
    } else {
      const errorData = await response.json()
      alert(`更新失败: ${errorData.detail || '未知错误'}`)
    }
  } catch (error) {
    console.error('Error updating staff:', error)
    alert('网络错误，更新失败')
  }
}

const toggleStaffStatus = async (nurse: Staff): Promise<void> => {
  const newStatus = !nurse.is_active;
  const actionText = getStaffToggleActionText(nurse.is_active);
  if (confirm(`确定要${actionText} "${nurse.name}" 吗？`)) {
    try {
      const response = await fetch(`/api/v1/nurses/${nurse.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: newStatus })
      });

      if (response.ok) {
        const index = staff.value.findIndex(s => s.id === nurse.id);
        if (index !== -1) {
          staff.value[index].is_active = newStatus;
        }
      } else {
        const errorData = await response.json();
        alert(`操作失败: ${errorData.detail || '未知错误'}`);
      }
    } catch (error) {
      console.error(`Error toggling staff status:`, error);
      alert('网络错误，操作失败');
    }
  }
}

const saveNewStaff = async () => {
  try {
    const response = await fetch('/api/v1/nurses/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newStaff.value)
    });

    if (response.ok) {
      console.log('Staff created successfully');
      closeAddStaffModal();
      fetchStaff();
    } else {
      console.error('Failed to create staff');
    }
  } catch (error) {
    console.error('Error creating staff:', error);
  }
};

const closeAddStaffModal = () => {
  showAddStaffModal.value = false;
  newStaff.value = {
    name: '',
    gender: '男',
    title: '',
    department: '',
    license_number: '',
    years_of_experience: 0,
    specialties: ''
  }
};

const prevPage = (): void => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// Watch for changes
watch([currentPage, sortOrder, departmentFilter, statusFilter], fetchStaff);

watchDebounced(
  searchQuery,
  () => {
    searchStaff();
  },
  { debounce: 300, maxWait: 1000 }
);

// Lifecycle
onMounted(() => {
  fetchStaff()
})
</script>

<style scoped>
/* Reusing styles from ServiceView.vue */
.staff-management {
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input, .filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table-row:hover {
  background: #f8f9fa;
}

.checkbox-col { width: 40px; }
.id-col { width: 60px; }
.name-col { width: 120px; }
.gender-col { width: 60px; }
.title-col { width: 120px; }
.department-col { width: 120px; }
.experience-col { width: 100px; }
.status-col { width: 80px; }
.actions-col { width: 200px; }

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #e7f5e7;
  color: #52c41a;
}

.status-inactive {
  background: #fff2e8;
  color: #fa8c16;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.btn-view {
  background: #1890ff;
  color: white;
}

.btn-edit {
  background: #52c41a;
  color: white;
}

.btn-enable {
  background: #52c41a;
  color: white;
}

.btn-disable {
  background: #ff4d4f;
  color: white;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-success {
  background: #52c41a;
  color: white;
}

.btn-secondary {
  background: #d9d9d9;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.close:hover {
  color: #333;
}

.view-details p {
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
}

.modal-content label {
  display: block;
  margin: 10px 0 5px 0;
  font-weight: 500;
}

.modal-content input,
.modal-content select,
.modal-content textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
}

.modal-content textarea {
  height: 80px;
  resize: vertical;
}
</style>
